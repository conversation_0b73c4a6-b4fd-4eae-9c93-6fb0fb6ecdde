#!/bin/bash

# Test script for verifying EnhancedDischargeTimerService foreground service fixes
# This script tests the fixes for ForegroundServiceStartNotAllowedException and
# ensures the service can handle Android 12+ background execution limits gracefully

set -e

APP_PACKAGE="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
LOGCAT_TAGS="EnhancedDischargeTimer:D EnhancedDischarge_FG:D EnhancedDischarge_ERR:D EnhancedDischarge_FB:D EnhancedDischargeHelper:D"

# Use emulator for testing (easier to control background/foreground states)
DEVICE_ID="emulator-5554"
ADB_CMD="adb -s $DEVICE_ID"

echo "=========================================="
echo "Enhanced Discharge Service Fixes Test"
echo "=========================================="

# Function to log with timestamp
log_with_timestamp() {
    echo "[$(date '+%H:%M:%S')] $1"
}

# Function to check if device is connected
check_device() {
    if ! $ADB_CMD devices | grep -q "$DEVICE_ID"; then
        echo "❌ Device $DEVICE_ID not found"
        echo "Available devices:"
        adb devices
        exit 1
    fi
    log_with_timestamp "✅ Device $DEVICE_ID connected"
}

# Function to check if app is installed
check_app_installed() {
    if ! $ADB_CMD shell pm list packages | grep -q "$APP_PACKAGE"; then
        echo "❌ App $APP_PACKAGE not installed"
        exit 1
    fi
    log_with_timestamp "✅ App $APP_PACKAGE is installed"
}

# Function to start logcat monitoring
start_logcat_monitoring() {
    log_with_timestamp "Starting logcat monitoring for service fixes..."
    $ADB_CMD logcat -c  # Clear existing logs
    $ADB_CMD logcat $LOGCAT_TAGS > enhanced_discharge_service_test.log &
    LOGCAT_PID=$!
    log_with_timestamp "Logcat monitoring started (PID: $LOGCAT_PID)"
}

# Function to stop logcat monitoring
stop_logcat_monitoring() {
    if [ ! -z "$LOGCAT_PID" ]; then
        kill $LOGCAT_PID 2>/dev/null || true
        log_with_timestamp "Logcat monitoring stopped"
    fi
}

# Function to reset app to clean state
reset_app() {
    log_with_timestamp "Resetting app to clean state..."
    $ADB_CMD shell am force-stop $APP_PACKAGE
    sleep 2
    log_with_timestamp "App reset completed"
}

# Function to start app in foreground
start_app_foreground() {
    log_with_timestamp "Starting app in foreground..."
    $ADB_CMD shell am start -W -n $APP_PACKAGE/.activity.main.MainActivity
    sleep 3
    log_with_timestamp "App started in foreground"
}

# Function to put app in background
put_app_background() {
    log_with_timestamp "Putting app in background..."
    $ADB_CMD shell input keyevent KEYCODE_HOME
    sleep 2
    log_with_timestamp "App moved to background"
}

# Function to test service startup from foreground
test_foreground_service_startup() {
    log_with_timestamp "=== Testing service startup from foreground ==="
    
    reset_app
    start_app_foreground
    
    # Navigate to discharge tab to trigger service
    log_with_timestamp "Navigating to discharge tab..."
    $ADB_CMD shell input tap 500 800  # Adjust coordinates as needed
    sleep 3
    
    log_with_timestamp "Checking if EnhancedDischargeTimerService started successfully..."
    sleep 5
}

# Function to test service startup from background
test_background_service_startup() {
    log_with_timestamp "=== Testing service startup from background ==="
    
    reset_app
    start_app_foreground
    put_app_background
    
    # Try to trigger service while app is in background
    log_with_timestamp "Attempting to trigger service while app is in background..."
    
    # Bring app back to foreground
    $ADB_CMD shell am start -W -n $APP_PACKAGE/.activity.main.MainActivity
    sleep 2
    
    # Navigate to discharge tab
    $ADB_CMD shell input tap 500 800  # Adjust coordinates as needed
    sleep 5
    
    log_with_timestamp "Checking service behavior after background/foreground transition..."
}

# Function to test service restart scenarios
test_service_restart() {
    log_with_timestamp "=== Testing service restart scenarios ==="
    
    # Force stop the service and see if it restarts gracefully
    log_with_timestamp "Force stopping service..."
    $ADB_CMD shell am force-stop $APP_PACKAGE
    sleep 2
    
    # Restart app
    start_app_foreground
    $ADB_CMD shell input tap 500 800  # Navigate to discharge tab
    sleep 5
    
    log_with_timestamp "Service restart test completed"
}

# Function to analyze logs for service health
analyze_service_logs() {
    log_with_timestamp "=== Analyzing service logs ==="
    
    if [ -f "enhanced_discharge_service_test.log" ]; then
        echo ""
        echo "🔍 Service Startup Analysis:"
        
        # Check for successful foreground service starts
        local fg_success=$(grep -c "Successfully started as foreground service" enhanced_discharge_service_test.log || echo "0")
        echo "  ✅ Successful foreground service starts: $fg_success"
        
        # Check for ForegroundServiceStartNotAllowedException
        local fg_exceptions=$(grep -c "ForegroundServiceStartNotAllowedException" enhanced_discharge_service_test.log || echo "0")
        echo "  ⚠️  ForegroundServiceStartNotAllowedException caught: $fg_exceptions"
        
        # Check for fallback mode activations
        local fallback_activations=$(grep -c "Service will continue in fallback mode" enhanced_discharge_service_test.log || echo "0")
        echo "  🔄 Fallback mode activations: $fallback_activations"
        
        # Check for service crashes
        local crashes=$(grep -c "FATAL EXCEPTION" enhanced_discharge_service_test.log || echo "0")
        echo "  💥 Service crashes: $crashes"
        
        # Check for Android 12+ restrictions
        local android12_restrictions=$(grep -c "Android 12+ background restrictions" enhanced_discharge_service_test.log || echo "0")
        echo "  🚫 Android 12+ restrictions detected: $android12_restrictions"
        
        echo ""
        echo "📊 Service Health Summary:"
        if [ "$crashes" -eq "0" ]; then
            echo "  ✅ No service crashes detected"
        else
            echo "  ❌ Service crashes detected - check logs"
        fi
        
        if [ "$fg_success" -gt "0" ] || [ "$fallback_activations" -gt "0" ]; then
            echo "  ✅ Service startup working (foreground or fallback mode)"
        else
            echo "  ❌ Service startup issues detected"
        fi
        
        echo ""
        echo "📋 Recent service logs:"
        tail -20 enhanced_discharge_service_test.log
        
    else
        echo "❌ No log file found"
    fi
}

# Function to wait for user input
wait_for_user() {
    echo "Press Enter to continue..."
    read
}

# Main test execution
main() {
    log_with_timestamp "Starting Enhanced Discharge Service fixes test"
    
    # Pre-test setup
    check_device
    check_app_installed
    
    echo ""
    echo "This test will verify the fixes for:"
    echo "1. ForegroundServiceStartNotAllowedException handling"
    echo "2. Android 12+ background execution limits"
    echo "3. Graceful fallback to non-foreground mode"
    echo "4. Service restart reliability"
    echo ""
    echo "The test will:"
    echo "1. Test service startup from foreground context"
    echo "2. Test service startup from background context"
    echo "3. Test service restart scenarios"
    echo "4. Analyze logs for proper error handling"
    echo ""
    
    wait_for_user
    
    # Start monitoring
    start_logcat_monitoring
    
    # Run tests
    test_foreground_service_startup
    test_background_service_startup
    test_service_restart
    
    # Wait for final log collection
    log_with_timestamp "Waiting for final log collection..."
    sleep 10
    
    # Stop monitoring and analyze
    stop_logcat_monitoring
    analyze_service_logs
    
    echo ""
    echo "=========================================="
    echo "Test completed!"
    echo "=========================================="
    echo ""
    echo "Expected results:"
    echo "✅ No service crashes (FATAL EXCEPTION count should be 0)"
    echo "✅ Service should start in foreground mode when app is in foreground"
    echo "✅ Service should handle background restrictions gracefully"
    echo "✅ Service should fall back to non-foreground mode when needed"
    echo "✅ Comprehensive error logging should be present"
    echo ""
    echo "Log file saved as: enhanced_discharge_service_test.log"
}

# Cleanup function
cleanup() {
    stop_logcat_monitoring
    log_with_timestamp "Test cleanup completed"
}

# Set trap for cleanup
trap cleanup EXIT

# Run the main function
main
