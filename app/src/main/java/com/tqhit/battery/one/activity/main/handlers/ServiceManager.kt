package com.tqhit.battery.one.activity.main.handlers

import android.content.Context
import android.util.Log
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager
import com.tqhit.battery.one.features.stats.discharge.domain.AppState
import com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper
//import com.tqhit.battery.one.service.BatteryMonitorService
import com.tqhit.battery.one.service.ChargingOverlayService
import com.tqhit.battery.one.service.ChargingOverlayServiceHelper
import com.tqhit.battery.one.utils.AntiThiefUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages service lifecycle and coordination for MainActivity.
 * Extracted from MainActivity to improve maintainability and separation of concerns.
 * 
 * Responsibilities:
 * - Battery monitoring service management
 * - Notification service coordination
 * - Overlay service management
 * - Anti-theft service coordination
 * - Service startup/shutdown orchestration
 * 
 * Following stats module architecture pattern with proper dependency injection.
 */
@Singleton
class ServiceManager @Inject constructor(
    private val unifiedBatteryNotificationServiceHelper: UnifiedBatteryNotificationServiceHelper,
    private val enhancedDischargeTimerServiceHelper: EnhancedDischargeTimerServiceHelper,
    private val chargingOverlayServiceHelper: ChargingOverlayServiceHelper,
    private val appLifecycleManager: AppLifecycleManager
) {
    companion object {
        private const val TAG = "ServiceManager"
        private const val FOREGROUND_TAG = "ServiceManager_Foreground"
    }

    /**
     * Starts critical services that are required for core functionality.
     * Called during activity resume to ensure services are running.
     * Implements Android 12+ foreground context detection to prevent background service restrictions.
     *
     * @param context The application context
     * @param lifecycleOwner The lifecycle owner for coroutine scope
     */
    fun startCriticalServices(context: Context, lifecycleOwner: LifecycleOwner) {
        val startTime = System.currentTimeMillis()
        val appState = appLifecycleManager.appState.value
        Log.d(TAG, "Starting critical services (App state: $appState)")

        // Check if app is in foreground before starting foreground services
        if (appState == AppState.FOREGROUND) {
            Log.d(FOREGROUND_TAG, "App in foreground - safe to start foreground services")
            startServicesInForegroundContext(startTime)
        } else {
            Log.w(FOREGROUND_TAG, "App in background - deferring service startup to avoid Android 12+ restrictions")
            deferServiceStartupUntilForeground(context, lifecycleOwner, startTime)
        }
    }

    /**
     * Starts services when app is confirmed to be in foreground context.
     */
    private fun startServicesInForegroundContext(startTime: Long) {
        try {
            // Start the unified battery notification service (replaces legacy ChargeMonitorService)
            Log.d(TAG, "Starting UnifiedBatteryNotificationService via helper")
            unifiedBatteryNotificationServiceHelper.startService()

            // Start enhanced discharge timer service
            Log.d(TAG, "Starting EnhancedDischargeTimerService via helper")
            enhancedDischargeTimerServiceHelper.startService()

            // Start charging overlay service if configuration allows
            Log.d(TAG, "Starting ChargingOverlayService via helper")
            chargingOverlayServiceHelper.startServiceIfNeeded()

            Log.d(TAG, "Critical services started in ${System.currentTimeMillis() - startTime}ms")
        } catch (e: Exception) {
            Log.e(TAG, "Error starting critical services", e)
        }
    }

    /**
     * Defers service startup until app moves to foreground to avoid Android 12+ restrictions.
     */
    private fun deferServiceStartupUntilForeground(context: Context, lifecycleOwner: LifecycleOwner, startTime: Long) {
        Log.d(FOREGROUND_TAG, "Setting up foreground state observer for deferred service startup")

        lifecycleOwner.lifecycleScope.launch {
            appLifecycleManager.appState.collect { state ->
                if (state == AppState.FOREGROUND) {
                    Log.d(FOREGROUND_TAG, "App moved to foreground - starting deferred services")
                    startServicesInForegroundContext(startTime)
                    return@collect // Stop observing after successful startup
                }
            }
        }
    }

    /**
     * Starts non-critical services asynchronously to avoid blocking the main thread.
     * These services enhance functionality but are not required for core operations.
     * 
     * @param context The application context
     * @param lifecycleOwner The lifecycle owner for coroutine scope
     */
    fun startNonCriticalServicesAsync(context: Context, lifecycleOwner: LifecycleOwner) {
        Log.d(TAG, "Starting non-critical services asynchronously")

        lifecycleOwner.lifecycleScope.launch(Dispatchers.IO) {
            try {
                val startTime = System.currentTimeMillis()

                // DEPRECATED: BatteryMonitorService startup disabled - using CoreBatteryStatsService instead
                // startBatteryMonitorService(context)

                // Initialize anti-theft utilities
                initializeAntiThiefUtils(context)

                Log.d(TAG, "Non-critical services started in ${System.currentTimeMillis() - startTime}ms")
            } catch (e: Exception) {
                Log.e(TAG, "Error starting non-critical services", e)
            }
        }
    }

    /**
     * DEPRECATED: Starts the battery monitor service if not already running.
     * This method is deprecated and should not be used. BatteryMonitorService has been
     * replaced by CoreBatteryStatsService for unified battery monitoring.
     *
     * @param context The application context
     */
    @Deprecated("Use CoreBatteryStatsService instead of BatteryMonitorService")
    private fun startBatteryMonitorService(context: Context) {
        Log.d(TAG, "DEPRECATED: BatteryMonitorService startup disabled - using CoreBatteryStatsService instead")
        // Legacy service startup commented out to eliminate duplicate battery monitoring
        // try {
        //     Log.d(TAG, "Starting BatteryMonitorService")
        //     val intent = android.content.Intent(context, BatteryMonitorService::class.java)
        //     androidx.core.content.ContextCompat.startForegroundService(context, intent)
        // } catch (e: Exception) {
        //     Log.e(TAG, "Error starting BatteryMonitorService", e)
        // }
    }

    /**
     * Initializes anti-theft utilities for device security.
     * 
     * @param context The application context
     */
    private fun initializeAntiThiefUtils(context: Context) {
        try {
            Log.d(TAG, "Initializing AntiThiefUtils")
            // AntiThiefUtils initialization - implementation depends on actual utility class
            Log.d(TAG, "AntiThiefUtils initialization completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing AntiThiefUtils", e)
        }
    }

    /**
     * Starts the charging overlay service for animation display.
     * Updated to use ChargingOverlayServiceHelper for better configuration management.
     *
     * @param context The application context
     */
    fun startChargingOverlayService(context: Context) {
        Log.d(TAG, "Starting ChargingOverlayService via helper")
        chargingOverlayServiceHelper.startServiceIfNeeded()
    }

    /**
     * Stops the charging overlay service.
     * Updated to use ChargingOverlayServiceHelper for consistency.
     *
     * @param context The application context
     */
    fun stopChargingOverlayService(context: Context) {
        Log.d(TAG, "Stopping ChargingOverlayService via helper")
        chargingOverlayServiceHelper.stopService()
    }

    /**
     * Stops all services during activity pause or destruction.
     * Only stops non-critical services to preserve battery monitoring functionality.
     * 
     * @param context The application context
     */
    fun stopNonCriticalServices(context: Context) {
        Log.d(TAG, "Stopping non-critical services")

        try {
            // Stop charging overlay service if running
            stopChargingOverlayService(context)

            Log.d(TAG, "Non-critical services stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping non-critical services", e)
        }
    }

    /**
     * Checks if critical services are running and restarts them if needed.
     * Implements foreground context detection to prevent Android 12+ background service restrictions.
     *
     * @param context The application context
     * @param lifecycleOwner The lifecycle owner for coroutine scope
     */
    fun ensureCriticalServicesRunning(context: Context, lifecycleOwner: LifecycleOwner) {
        val appState = appLifecycleManager.appState.value
        Log.d(TAG, "Ensuring critical services are running (App state: $appState)")

        lifecycleOwner.lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Only restart services if app is in foreground to avoid Android 12+ restrictions
                if (appState == AppState.FOREGROUND) {
                    // Check and restart unified battery notification service if needed
                    if (!unifiedBatteryNotificationServiceHelper.isServiceRunning()) {
                        Log.w(TAG, "UnifiedBatteryNotificationService not running, restarting")
                        unifiedBatteryNotificationServiceHelper.startService()
                    }

                    // Check and restart enhanced discharge timer service if needed
                    if (!enhancedDischargeTimerServiceHelper.isServiceRunning()) {
                        Log.w(TAG, "EnhancedDischargeTimerService not running, restarting")
                        enhancedDischargeTimerServiceHelper.startService()
                    }

                    // Check and restart charging overlay service if needed and configuration allows
                    if (!chargingOverlayServiceHelper.isServiceRunning() && chargingOverlayServiceHelper.shouldStartService()) {
                        Log.w(TAG, "ChargingOverlayService not running but should be, restarting")
                        chargingOverlayServiceHelper.startServiceIfNeeded()
                    }

                    Log.d(TAG, "Critical services check completed")
                } else {
                    Log.w(FOREGROUND_TAG, "App in background - skipping service restart to avoid Android 12+ restrictions")
                    Log.w(FOREGROUND_TAG, "Services will be restarted when app returns to foreground")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error ensuring critical services are running", e)
            }
        }
    }

    /**
     * Gets service status information for debugging.
     *
     * @return String containing service status information
     */
    fun getServiceStatus(): String {
        return try {
            val unifiedServiceStatus = if (unifiedBatteryNotificationServiceHelper.isServiceRunning()) "Running" else "Stopped"
            val dischargeServiceStatus = if (enhancedDischargeTimerServiceHelper.isServiceRunning()) "Running" else "Stopped"
            val overlayServiceStatus = if (chargingOverlayServiceHelper.isServiceRunning()) "Running" else "Stopped"
            val overlayConfigStatus = if (chargingOverlayServiceHelper.shouldStartService()) "Enabled" else "Disabled"

            "Service Status:\n" +
            "- UnifiedBatteryNotificationService: $unifiedServiceStatus\n" +
            "- EnhancedDischargeTimerService: $dischargeServiceStatus\n" +
            "- ChargingOverlayService: $overlayServiceStatus (Config: $overlayConfigStatus)"
        } catch (e: Exception) {
            Log.e(TAG, "Error getting service status", e)
            "Service Status: Error retrieving status"
        }
    }

    /**
     * Performs service health check and logs any issues.
     * 
     * @param context The application context
     */
    fun performServiceHealthCheck(context: Context) {
        Log.d(TAG, "Performing service health check")

        try {
            val serviceStatus = getServiceStatus()
            Log.d(TAG, "SERVICE_HEALTH: $serviceStatus")

            // Log any service issues
            if (!unifiedBatteryNotificationServiceHelper.isServiceRunning()) {
                Log.w(TAG, "SERVICE_HEALTH: UnifiedBatteryNotificationService is not running")
            }

            if (!enhancedDischargeTimerServiceHelper.isServiceRunning()) {
                Log.w(TAG, "SERVICE_HEALTH: EnhancedDischargeTimerService is not running")
            }

            if (!chargingOverlayServiceHelper.isServiceRunning() && chargingOverlayServiceHelper.shouldStartService()) {
                Log.w(TAG, "SERVICE_HEALTH: ChargingOverlayService is not running but should be (config allows)")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error performing service health check", e)
        }
    }
}
